import { Test, TestingModule } from '@nestjs/testing';
import { getModelToken } from '@nestjs/mongoose';
import { QuestionPool } from '../modules/mongodb/schemas/question-pool.schema';
import { QuestionPoolMigrationService, MigrationStats } from './migrate-question-pool-fields';

/**
 * Test suite for the Question Pool Field Migration Script
 * Tests the migration logic that fixes field inconsistency between
 * optionValue.value and childSubject fields
 */
describe('QuestionPoolMigrationService', () => {
  let migrationService: QuestionPoolMigrationService;
  let mockQuestionPoolModel: any;

  // Mock documents representing the current state of the database
  const mockDocumentsNeedingMigration = [
    {
      _id: '507f1f77bcf86cd799439011',
      content: 'Question with optionValue only',
      type: 'multiple_choice',
      optionValue: { value: 'Addition', id: 'opt_123' },
      // No childSubject field
      subject: 'Mathematics',
      grade: 'Primary 2',
      status: 'active',
    },
    {
      _id: '507f1f77bcf86cd799439012',
      content: 'Question with optionValue only',
      type: 'fill_blank',
      optionValue: { value: 'Multiplication', id: 'opt_456' },
      // No childSubject field
      subject: 'Mathematics',
      grade: 'Primary 3',
      status: 'active',
    },
    {
      _id: '507f1f77bcf86cd799439013',
      content: 'Question with empty childSubject',
      type: 'multiple_choice',
      optionValue: { value: 'Algebra', id: 'opt_789' },
      childSubject: '', // Empty childSubject
      subject: 'Mathematics',
      grade: 'Secondary 1',
      status: 'active',
    },
  ];

  const mockDocumentsAlreadyMigrated = [
    {
      _id: '507f1f77bcf86cd799439014',
      content: 'Question already migrated',
      type: 'multiple_choice',
      optionValue: { value: 'Geometry', id: 'opt_101' },
      childSubject: 'Geometry', // Already has childSubject
      subject: 'Mathematics',
      grade: 'Primary 4',
      status: 'active',
    },
  ];

  beforeEach(async () => {
    // Create mock model with all necessary methods
    mockQuestionPoolModel = {
      countDocuments: jest.fn(),
      find: jest.fn().mockReturnValue({
        limit: jest.fn().mockReturnValue({
          skip: jest.fn().mockReturnValue({
            exec: jest.fn(),
          }),
        }),
      }),
      updateOne: jest.fn(),
      updateMany: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        {
          provide: getModelToken(QuestionPool.name),
          useValue: mockQuestionPoolModel,
        },
      ],
    }).compile();

    const questionPoolModel = module.get(getModelToken(QuestionPool.name));
    migrationService = new QuestionPoolMigrationService(questionPoolModel);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Migration Analysis', () => {
    it('should correctly analyze the current database state', async () => {
      // Mock count queries
      mockQuestionPoolModel.countDocuments
        .mockResolvedValueOnce(13939) // Total documents
        .mockResolvedValueOnce(13141) // Documents with optionValue.value
        .mockResolvedValueOnce(798)   // Documents with childSubject
        .mockResolvedValueOnce(13141); // Documents needing migration

      const stats: MigrationStats = {
        totalDocuments: 0,
        documentsWithOptionValue: 0,
        documentsWithChildSubject: 0,
        documentsNeedingMigration: 0,
        documentsUpdated: 0,
        documentsSkipped: 0,
        errors: 0,
        batchesProcessed: 0,
        totalProcessingTime: 0,
      };

      // Call the private method through reflection for testing
      await (migrationService as any).analyzeCurrentState(stats);

      expect(stats.totalDocuments).toBe(13939);
      expect(stats.documentsWithOptionValue).toBe(13141);
      expect(stats.documentsWithChildSubject).toBe(798);
      expect(stats.documentsNeedingMigration).toBe(13141);

      // Verify the correct queries were made
      expect(mockQuestionPoolModel.countDocuments).toHaveBeenCalledWith({});
      expect(mockQuestionPoolModel.countDocuments).toHaveBeenCalledWith({
        'optionValue.value': { $exists: true, $ne: null }
      });
      expect(mockQuestionPoolModel.countDocuments).toHaveBeenCalledWith({
        childSubject: { $exists: true, $ne: null }
      });
      expect(mockQuestionPoolModel.countDocuments).toHaveBeenCalledWith({
        'optionValue.value': { $exists: true, $ne: null },
        $or: [
          { childSubject: { $exists: false } },
          { childSubject: null },
          { childSubject: '' }
        ]
      });
    });

    it('should handle case where no migration is needed', async () => {
      // Mock scenario where all documents are already migrated
      mockQuestionPoolModel.countDocuments
        .mockResolvedValueOnce(1000)  // Total documents
        .mockResolvedValueOnce(1000)  // Documents with optionValue.value
        .mockResolvedValueOnce(1000)  // Documents with childSubject
        .mockResolvedValueOnce(0);    // Documents needing migration

      const stats: MigrationStats = {
        totalDocuments: 0,
        documentsWithOptionValue: 0,
        documentsWithChildSubject: 0,
        documentsNeedingMigration: 0,
        documentsUpdated: 0,
        documentsSkipped: 0,
        errors: 0,
        batchesProcessed: 0,
        totalProcessingTime: 0,
      };

      await (migrationService as any).analyzeCurrentState(stats);

      expect(stats.documentsNeedingMigration).toBe(0);
    });
  });

  describe('Migration Execution', () => {
    it('should successfully migrate documents in batches', async () => {
      // Mock the find query to return documents needing migration
      const mockExec = jest.fn()
        .mockResolvedValueOnce(mockDocumentsNeedingMigration.slice(0, 2)) // First batch
        .mockResolvedValueOnce(mockDocumentsNeedingMigration.slice(2, 3)) // Second batch
        .mockResolvedValueOnce([]); // No more documents

      mockQuestionPoolModel.find.mockReturnValue({
        limit: jest.fn().mockReturnValue({
          skip: jest.fn().mockReturnValue({
            exec: mockExec,
          }),
        }),
      });

      // Mock successful updates
      mockQuestionPoolModel.updateOne.mockResolvedValue({ acknowledged: true });

      const stats: MigrationStats = {
        totalDocuments: 0,
        documentsWithOptionValue: 0,
        documentsWithChildSubject: 0,
        documentsNeedingMigration: 3,
        documentsUpdated: 0,
        documentsSkipped: 0,
        errors: 0,
        batchesProcessed: 0,
        totalProcessingTime: 0,
      };

      // Set a smaller batch size for testing
      (migrationService as any).BATCH_SIZE = 2;

      await (migrationService as any).performMigration(stats);

      expect(stats.documentsUpdated).toBe(3);
      expect(stats.batchesProcessed).toBe(2);
      expect(stats.errors).toBe(0);

      // Verify correct update calls
      expect(mockQuestionPoolModel.updateOne).toHaveBeenCalledWith(
        { _id: '507f1f77bcf86cd799439011' },
        { $set: { childSubject: 'Addition' } }
      );
      expect(mockQuestionPoolModel.updateOne).toHaveBeenCalledWith(
        { _id: '507f1f77bcf86cd799439012' },
        { $set: { childSubject: 'Multiplication' } }
      );
      expect(mockQuestionPoolModel.updateOne).toHaveBeenCalledWith(
        { _id: '507f1f77bcf86cd799439013' },
        { $set: { childSubject: 'Algebra' } }
      );
    });

    it('should handle documents with missing optionValue.value gracefully', async () => {
      const documentsWithMissingValue = [
        {
          _id: '507f1f77bcf86cd799439020',
          content: 'Question with empty optionValue.value',
          type: 'multiple_choice',
          optionValue: { id: 'opt_empty' }, // Missing value field
          subject: 'Mathematics',
          grade: 'Primary 1',
          status: 'active',
        },
      ];

      const mockExec = jest.fn()
        .mockResolvedValueOnce(documentsWithMissingValue)
        .mockResolvedValueOnce([]);

      mockQuestionPoolModel.find.mockReturnValue({
        limit: jest.fn().mockReturnValue({
          skip: jest.fn().mockReturnValue({
            exec: mockExec,
          }),
        }),
      });

      const stats: MigrationStats = {
        totalDocuments: 0,
        documentsWithOptionValue: 0,
        documentsWithChildSubject: 0,
        documentsNeedingMigration: 1,
        documentsUpdated: 0,
        documentsSkipped: 0,
        errors: 0,
        batchesProcessed: 0,
        totalProcessingTime: 0,
      };

      await (migrationService as any).performMigration(stats);

      expect(stats.documentsSkipped).toBe(1);
      expect(stats.documentsUpdated).toBe(0);
      expect(mockQuestionPoolModel.updateOne).not.toHaveBeenCalled();
    });

    it('should handle database errors during migration', async () => {
      const mockExec = jest.fn()
        .mockResolvedValueOnce([mockDocumentsNeedingMigration[0]])
        .mockResolvedValueOnce([]);

      mockQuestionPoolModel.find.mockReturnValue({
        limit: jest.fn().mockReturnValue({
          skip: jest.fn().mockReturnValue({
            exec: mockExec,
          }),
        }),
      });

      // Mock database error
      mockQuestionPoolModel.updateOne.mockRejectedValue(new Error('Database connection failed'));

      const stats: MigrationStats = {
        totalDocuments: 0,
        documentsWithOptionValue: 0,
        documentsWithChildSubject: 0,
        documentsNeedingMigration: 1,
        documentsUpdated: 0,
        documentsSkipped: 0,
        errors: 0,
        batchesProcessed: 0,
        totalProcessingTime: 0,
      };

      await (migrationService as any).performMigration(stats);

      expect(stats.errors).toBe(1);
      expect(stats.documentsUpdated).toBe(0);
    });
  });

  describe('Migration Verification', () => {
    it('should verify migration results correctly', async () => {
      // Mock post-migration counts
      mockQuestionPoolModel.countDocuments
        .mockResolvedValueOnce(0)    // Documents still needing migration
        .mockResolvedValueOnce(13939); // Documents with both fields

      const stats: MigrationStats = {
        totalDocuments: 13939,
        documentsWithOptionValue: 13141,
        documentsWithChildSubject: 798,
        documentsNeedingMigration: 13141,
        documentsUpdated: 13141,
        documentsSkipped: 0,
        errors: 0,
        batchesProcessed: 132,
        totalProcessingTime: 45000,
      };

      await (migrationService as any).verifyMigration(stats);

      // Verify the correct verification queries were made
      expect(mockQuestionPoolModel.countDocuments).toHaveBeenCalledWith({
        'optionValue.value': { $exists: true, $ne: null },
        $or: [
          { childSubject: { $exists: false } },
          { childSubject: null },
          { childSubject: '' }
        ]
      });
      expect(mockQuestionPoolModel.countDocuments).toHaveBeenCalledWith({
        'optionValue.value': { $exists: true, $ne: null },
        childSubject: { $exists: true, $ne: null }
      });
    });
  });

  describe('Full Migration Process', () => {
    it('should complete full migration successfully', async () => {
      // Mock analysis phase
      mockQuestionPoolModel.countDocuments
        .mockResolvedValueOnce(100)  // Total documents
        .mockResolvedValueOnce(50)   // Documents with optionValue.value
        .mockResolvedValueOnce(25)   // Documents with childSubject
        .mockResolvedValueOnce(25)   // Documents needing migration
        // Mock verification phase
        .mockResolvedValueOnce(0)    // Documents still needing migration
        .mockResolvedValueOnce(50);  // Documents with both fields

      // Mock migration execution
      const mockExec = jest.fn()
        .mockResolvedValueOnce([mockDocumentsNeedingMigration[0]])
        .mockResolvedValueOnce([]);

      mockQuestionPoolModel.find.mockReturnValue({
        limit: jest.fn().mockReturnValue({
          skip: jest.fn().mockReturnValue({
            exec: mockExec,
          }),
        }),
      });

      mockQuestionPoolModel.updateOne.mockResolvedValue({ acknowledged: true });

      // Set DRY_RUN to false for actual migration
      (migrationService as any).DRY_RUN = false;

      const result = await migrationService.migrate();

      expect(result.totalDocuments).toBe(100);
      expect(result.documentsNeedingMigration).toBe(25);
      expect(result.documentsUpdated).toBe(1);
      expect(result.errors).toBe(0);
      expect(result.totalProcessingTime).toBeGreaterThan(0);
    });

    it('should handle dry run mode correctly', async () => {
      // Mock analysis phase
      mockQuestionPoolModel.countDocuments
        .mockResolvedValueOnce(100)  // Total documents
        .mockResolvedValueOnce(50)   // Documents with optionValue.value
        .mockResolvedValueOnce(25)   // Documents with childSubject
        .mockResolvedValueOnce(25)   // Documents needing migration
        // Mock verification phase
        .mockResolvedValueOnce(25)   // Documents still needing migration (unchanged in dry run)
        .mockResolvedValueOnce(25);  // Documents with both fields

      // Set DRY_RUN to true
      (migrationService as any).DRY_RUN = true;

      const result = await migrationService.migrate();

      expect(result.totalDocuments).toBe(100);
      expect(result.documentsNeedingMigration).toBe(25);
      expect(result.documentsUpdated).toBe(0); // No updates in dry run
      expect(result.errors).toBe(0);

      // Verify no actual updates were performed
      expect(mockQuestionPoolModel.updateOne).not.toHaveBeenCalled();
    });
  });
});
