import { NestFactory } from '@nestjs/core';
import { AppModule } from '../app.module';
import { ComprehensiveQuestionPoolMigrationService } from './fix-missing-grades';
import { WorksheetQueryTester } from './test-worksheet-query';
import { QuestionPoolService } from '../modules/question-pool/question-pool.service';
import { Logger } from '@nestjs/common';

/**
 * Complete fix script that:
 * 1. Runs the migration to clean up question pool
 * 2. Tests the worksheet query functionality
 * 3. Verifies everything works end-to-end
 */

class CompleteFixer {
  private readonly logger = new Logger(CompleteFixer.name);

  constructor(
    private readonly migrationService: ComprehensiveQuestionPoolMigrationService,
    private readonly questionPoolService: QuestionPoolService,
  ) {}

  async runCompleteFix(): Promise<void> {
    this.logger.log('🚀 Starting complete question pool fix...\n');

    try {
      // Step 1: Show current state
      console.log('='.repeat(60));
      console.log('📊 CURRENT DATABASE STATE');
      console.log('='.repeat(60));
      await this.showCurrentState();

      // Step 2: Ask for confirmation
      console.log('\n' + '='.repeat(60));
      console.log('⚠️  MIGRATION WARNING');
      console.log('='.repeat(60));
      console.log('This will:');
      console.log('• DELETE ALL non-Mathematics questions permanently');
      console.log('• Set ALL Mathematics questions to grade P6');
      console.log('• This action CANNOT be undone');
      console.log('\nProceeding in 5 seconds...');
      
      // Wait 5 seconds
      await new Promise(resolve => setTimeout(resolve, 5000));

      // Step 3: Run migration
      console.log('\n' + '='.repeat(60));
      console.log('🔧 RUNNING MIGRATION');
      console.log('='.repeat(60));
      const migrationStats = await this.migrationService.migrateQuestionPool();

      // Step 4: Verify migration
      console.log('\n' + '='.repeat(60));
      console.log('✅ VERIFYING MIGRATION');
      console.log('='.repeat(60));
      await this.migrationService.verifyMigration();

      // Step 5: Test worksheet functionality
      console.log('\n' + '='.repeat(60));
      console.log('🧪 TESTING WORKSHEET FUNCTIONALITY');
      console.log('='.repeat(60));
      const tester = new WorksheetQueryTester(this.questionPoolService);
      await tester.testPostMigrationState();
      await tester.testWorksheetQuery();
      await tester.testFullWorksheetFlow();

      // Step 6: Final summary
      console.log('\n' + '='.repeat(60));
      console.log('📋 FINAL SUMMARY');
      console.log('='.repeat(60));
      console.log(`✅ Migration completed successfully!`);
      console.log(`📊 Questions before: ${migrationStats.totalQuestionsBeforeMigration}`);
      console.log(`🗑️  Non-Math removed: ${migrationStats.nonMathQuestionsRemoved}`);
      console.log(`📝 Math questions updated: ${migrationStats.mathQuestionsUpdated}`);
      console.log(`📊 Questions after: ${migrationStats.totalQuestionsAfterMigration}`);
      console.log(`❌ Errors: ${migrationStats.errors}`);
      
      if (migrationStats.errors === 0) {
        console.log('\n🎉 SUCCESS: Question pool is now optimized for P6 Mathematics worksheets!');
        console.log('🔧 Cron job updated to only generate P6 Mathematics questions');
        console.log('🧪 Worksheet creation should now work properly with the provided payload');
      } else {
        console.log(`\n⚠️  Migration completed with ${migrationStats.errors} errors. Check logs for details.`);
      }

    } catch (error) {
      this.logger.error(`❌ Complete fix failed: ${error.message}`, error.stack);
      throw error;
    }
  }

  private async showCurrentState(): Promise<void> {
    try {
      // Get total questions by subject
      const allQuestions = await this.questionPoolService.getQuestions({}, 1);
      console.log(`Total questions: ${allQuestions.total}`);

      // Get questions by subject (this requires a custom aggregation)
      // For now, let's check specific subjects
      const subjects = ['Mathematics', 'English', 'Chinese', 'Science', 'Art'];
      
      for (const subject of subjects) {
        try {
          const subjectQuestions = await this.questionPoolService.getQuestions({ subject }, 1);
          console.log(`${subject}: ${subjectQuestions.total} questions`);
        } catch (error) {
          console.log(`${subject}: Error getting count`);
        }
      }

      // Check Mathematics topics
      console.log('\nMathematics topics:');
      const mathTopics = ['Speed', 'Percentage', 'Decimals', 'Fractions', 'Algebra'];
      
      for (const topic of mathTopics) {
        try {
          const topicQuestions = await this.questionPoolService.getQuestions({
            subject: 'Mathematics',
            childSubject: topic
          }, 1);
          console.log(`  ${topic}: ${topicQuestions.total} questions`);
        } catch (error) {
          console.log(`  ${topic}: Error getting count`);
        }
      }

      // Check P6 Mathematics questions
      try {
        const p6MathQuestions = await this.questionPoolService.getQuestions({
          subject: 'Mathematics',
          grade: 'P6'
        }, 1);
        console.log(`\nP6 Mathematics questions: ${p6MathQuestions.total}`);
      } catch (error) {
        console.log('\nP6 Mathematics questions: Error getting count');
      }

    } catch (error) {
      this.logger.error(`Error showing current state: ${error.message}`);
    }
  }
}

async function runCompleteFix() {
  console.log('🔧 Question Pool Complete Fix Tool');
  console.log('==================================\n');

  const app = await NestFactory.createApplicationContext(AppModule);
  
  try {
    const migrationService = app.get(ComprehensiveQuestionPoolMigrationService);
    const questionPoolService = app.get(QuestionPoolService);
    const fixer = new CompleteFixer(migrationService, questionPoolService);

    await fixer.runCompleteFix();
    
  } catch (error) {
    console.error('❌ Complete fix failed:', error);
    process.exit(1);
  } finally {
    await app.close();
  }
}

// Run the complete fix if this file is executed directly
if (require.main === module) {
  runCompleteFix();
}

export { CompleteFixer };
