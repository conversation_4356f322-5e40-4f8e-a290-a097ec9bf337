import { NestFactory } from '@nestjs/core';
import { AppModule } from '../app.module';
import { QuestionPoolService } from '../modules/question-pool/question-pool.service';
import { Logger } from '@nestjs/common';

/**
 * Test script to verify that worksheet creation with P6 Mathematics payload
 * can successfully query questions from the pool for Speed, Percentage, and Decimals topics
 */

class WorksheetQueryTester {
  private readonly logger = new Logger(WorksheetQueryTester.name);

  constructor(
    private readonly questionPoolService: QuestionPoolService,
  ) {}

  /**
   * Test the exact query that would be made during worksheet creation
   */
  async testWorksheetQuery(): Promise<void> {
    this.logger.log('Testing worksheet query for P6 Mathematics with Speed, Percentage, Decimals...');

    // Test each topic individually
    const topics = ['Speed', 'Percentage', 'Decimals'];
    
    for (const topic of topics) {
      await this.testTopicQuery(topic, 'P6', 'English');
    }

    // Test combined query (what the system would actually do)
    await this.testCombinedQuery(topics, 'P6', 'English');
  }

  /**
   * Test query for a specific topic
   */
  private async testTopicQuery(topic: string, grade: string, language: string): Promise<void> {
    this.logger.log(`\n--- Testing ${topic} ---`);

    try {
      const filters = {
        subject: 'Mathematics',
        childSubject: topic,
        grade: grade,
        language: language,
        status: 'active'
      };

      this.logger.log(`Query filters: ${JSON.stringify(filters)}`);

      const result = await this.questionPoolService.getQuestions(filters, 10);
      
      this.logger.log(`✅ ${topic}: Found ${result.questions.length} questions (total available: ${result.total})`);
      
      if (result.questions.length > 0) {
        // Show sample question
        const sample = result.questions[0];
        this.logger.log(`Sample question: ${sample.content?.substring(0, 100)}...`);
        this.logger.log(`Question type: ${sample.type}`);
        this.logger.log(`Grade: ${sample.grade || 'undefined'}`);
      } else {
        this.logger.warn(`❌ No questions found for ${topic}`);
      }

    } catch (error) {
      this.logger.error(`❌ Error querying ${topic}: ${error.message}`);
    }
  }

  /**
   * Test combined query using random selection (what worksheet generation uses)
   */
  private async testCombinedQuery(topics: string[], grade: string, language: string): Promise<void> {
    this.logger.log(`\n--- Testing Combined Random Selection ---`);

    for (const topic of topics) {
      try {
        const filters = {
          subject: 'Mathematics',
          childSubject: topic,
          grade: grade,
          language: language,
          status: 'active'
        };

        // Test random selection (what the worksheet generation actually uses)
        const randomQuestions = await this.questionPoolService.getRandomQuestions(filters, 3);
        
        this.logger.log(`✅ Random selection for ${topic}: ${randomQuestions.length} questions`);
        
        if (randomQuestions.length > 0) {
          const types = randomQuestions.map(q => q.type);
          this.logger.log(`Question types: ${types.join(', ')}`);
        }

      } catch (error) {
        this.logger.error(`❌ Error in random selection for ${topic}: ${error.message}`);
      }
    }
  }

  /**
   * Test the full worksheet generation flow simulation
   */
  async testFullWorksheetFlow(): Promise<void> {
    this.logger.log(`\n=== FULL WORKSHEET FLOW TEST ===`);

    const totalQuestionsNeeded = 10;
    const questionsPerTopic = Math.ceil(totalQuestionsNeeded / 3);
    const topics = ['Speed', 'Percentage', 'Decimals'];
    let totalFound = 0;
    const questionsByType = { single_choice: 0, multiple_choice: 0, fill_blank: 0, other: 0 };

    for (const topic of topics) {
      try {
        const filters = {
          subject: 'Mathematics',
          childSubject: topic,
          grade: 'P6',
          language: 'English',
          status: 'active'
        };

        const questions = await this.questionPoolService.getRandomQuestions(filters, questionsPerTopic);
        totalFound += questions.length;
        
        // Count question types
        questions.forEach(q => {
          if (q.type === 'single_choice') questionsByType.single_choice++;
          else if (q.type === 'multiple_choice') questionsByType.multiple_choice++;
          else if (q.type === 'fill_blank') questionsByType.fill_blank++;
          else questionsByType.other++;
        });
        
        this.logger.log(`${topic}: ${questions.length}/${questionsPerTopic} questions found`);

      } catch (error) {
        this.logger.error(`Error getting questions for ${topic}: ${error.message}`);
      }
    }

    this.logger.log(`\n📊 SUMMARY: Found ${totalFound}/${totalQuestionsNeeded} total questions`);
    this.logger.log(`Question types: Single Choice: ${questionsByType.single_choice}, Multiple Choice: ${questionsByType.multiple_choice}, Fill Blank: ${questionsByType.fill_blank}, Other: ${questionsByType.other}`);
    
    if (totalFound >= totalQuestionsNeeded) {
      this.logger.log(`✅ SUCCESS: Worksheet can be created with sufficient questions from pool!`);
      
      const targetSingleChoice = 7;
      const targetMultipleChoice = 3;
      
      if (questionsByType.single_choice >= targetSingleChoice && questionsByType.multiple_choice >= targetMultipleChoice) {
        this.logger.log(`✅ BONUS: Question type distribution matches payload requirements!`);
      } else {
        this.logger.warn(`⚠️  Question type distribution may not match payload (need ${targetSingleChoice} single choice, ${targetMultipleChoice} multiple choice)`);
      }
    } else {
      this.logger.warn(`⚠️  PARTIAL: Only ${totalFound} questions available, ${totalQuestionsNeeded - totalFound} would need AI generation`);
    }
  }

  /**
   * Test database state after migration
   */
  async testPostMigrationState(): Promise<void> {
    this.logger.log(`\n=== POST-MIGRATION DATABASE STATE ===`);

    try {
      // Check total questions
      const totalQuestions = await this.questionPoolService.getQuestions({}, 1);
      this.logger.log(`Total questions in pool: ${totalQuestions.total}`);

      // Check Mathematics questions
      const mathQuestions = await this.questionPoolService.getQuestions({ subject: 'Mathematics' }, 1);
      this.logger.log(`Mathematics questions: ${mathQuestions.total}`);

      // Check P6 Mathematics questions
      const p6MathQuestions = await this.questionPoolService.getQuestions({ 
        subject: 'Mathematics', 
        grade: 'P6' 
      }, 1);
      this.logger.log(`P6 Mathematics questions: ${p6MathQuestions.total}`);

      // Check specific topics
      const topics = ['Speed', 'Percentage', 'Decimals'];
      for (const topic of topics) {
        const topicQuestions = await this.questionPoolService.getQuestions({
          subject: 'Mathematics',
          childSubject: topic,
          grade: 'P6'
        }, 1);
        this.logger.log(`P6 Mathematics ${topic} questions: ${topicQuestions.total}`);
      }

      // Check for any non-Mathematics questions (should be 0 after migration)
      const nonMathQuestions = await this.questionPoolService.getQuestions({
        subject: { $ne: 'Mathematics' }
      } as any, 1);
      
      if (nonMathQuestions.total === 0) {
        this.logger.log(`✅ No non-Mathematics questions found (migration successful)`);
      } else {
        this.logger.warn(`⚠️  ${nonMathQuestions.total} non-Mathematics questions still exist`);
      }

    } catch (error) {
      this.logger.error(`Error checking post-migration state: ${error.message}`);
    }
  }
}

async function runTest() {
  const app = await NestFactory.createApplicationContext(AppModule);
  const questionPoolService = app.get(QuestionPoolService);
  const tester = new WorksheetQueryTester(questionPoolService);

  try {
    await tester.testPostMigrationState();
    await tester.testWorksheetQuery();
    await tester.testFullWorksheetFlow();
    
  } catch (error) {
    console.error('Test failed:', error);
    process.exit(1);
  } finally {
    await app.close();
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  runTest();
}

export { WorksheetQueryTester };
