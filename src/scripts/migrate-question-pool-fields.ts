import { NestFactory } from '@nestjs/core';
import { AppModule } from '../app.module';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { QuestionPool } from '../modules/mongodb/schemas/question-pool.schema';
import { Logger } from '@nestjs/common';

/**
 * Migration script to fix question pool field inconsistency
 * Updates documents where optionValue.value exists but childSubject is missing
 * 
 * This script addresses the critical bug where 94.3% of questions (13,141 out of 13,939)
 * cannot be retrieved due to field naming inconsistency between 'optionValue' and 'childSubject'
 */

interface MigrationStats {
  totalDocuments: number;
  documentsWithOptionValue: number;
  documentsWithChildSubject: number;
  documentsNeedingMigration: number;
  documentsUpdated: number;
  documentsSkipped: number;
  errors: number;
  batchesProcessed: number;
  totalProcessingTime: number;
}

class QuestionPoolMigrationService {
  private readonly logger = new Logger(QuestionPoolMigrationService.name);
  private readonly BATCH_SIZE = 100; // Process 100 documents at a time
  private readonly DRY_RUN = process.env.DRY_RUN === 'true'; // Set DRY_RUN=true to test without making changes

  constructor(
    @InjectModel(QuestionPool.name) private questionPoolModel: Model<QuestionPool>
  ) {}

  /**
   * Main migration method
   */
  async migrate(): Promise<MigrationStats> {
    const startTime = Date.now();
    this.logger.log('Starting Question Pool Field Migration...');
    
    if (this.DRY_RUN) {
      this.logger.warn('DRY RUN MODE: No actual changes will be made to the database');
    }

    const stats: MigrationStats = {
      totalDocuments: 0,
      documentsWithOptionValue: 0,
      documentsWithChildSubject: 0,
      documentsNeedingMigration: 0,
      documentsUpdated: 0,
      documentsSkipped: 0,
      errors: 0,
      batchesProcessed: 0,
      totalProcessingTime: 0,
    };

    try {
      // Step 1: Analyze current state
      await this.analyzeCurrentState(stats);

      // Step 2: Perform migration in batches
      if (!this.DRY_RUN && stats.documentsNeedingMigration > 0) {
        await this.performMigration(stats);
      }

      // Step 3: Verify migration results
      await this.verifyMigration(stats);

      stats.totalProcessingTime = Date.now() - startTime;
      this.logFinalStats(stats);

      return stats;
    } catch (error) {
      this.logger.error(`Migration failed: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Analyze the current state of the database
   */
  private async analyzeCurrentState(stats: MigrationStats): Promise<void> {
    this.logger.log('Analyzing current database state...');

    // Count total documents
    stats.totalDocuments = await this.questionPoolModel.countDocuments({});
    this.logger.log(`Total documents in question pool: ${stats.totalDocuments}`);

    // Count documents with optionValue.value
    stats.documentsWithOptionValue = await this.questionPoolModel.countDocuments({
      'optionValue.value': { $exists: true, $ne: null }
    });
    this.logger.log(`Documents with optionValue.value: ${stats.documentsWithOptionValue}`);

    // Count documents with childSubject
    stats.documentsWithChildSubject = await this.questionPoolModel.countDocuments({
      childSubject: { $exists: true, $ne: null }
    });
    this.logger.log(`Documents with childSubject: ${stats.documentsWithChildSubject}`);

    // Count documents needing migration (have optionValue.value but missing childSubject)
    stats.documentsNeedingMigration = await this.questionPoolModel.countDocuments({
      'optionValue.value': { $exists: true, $ne: null },
      $or: [
        { childSubject: { $exists: false } },
        { childSubject: null },
        { childSubject: '' }
      ]
    });
    this.logger.log(`Documents needing migration: ${stats.documentsNeedingMigration}`);

    if (stats.documentsNeedingMigration === 0) {
      this.logger.log('No documents need migration. All documents already have consistent fields.');
    }
  }

  /**
   * Perform the actual migration in batches
   */
  private async performMigration(stats: MigrationStats): Promise<void> {
    this.logger.log(`Starting migration of ${stats.documentsNeedingMigration} documents...`);

    let skip = 0;
    let batchNumber = 1;

    while (skip < stats.documentsNeedingMigration) {
      const batchStartTime = Date.now();
      this.logger.log(`Processing batch ${batchNumber} (documents ${skip + 1} to ${Math.min(skip + this.BATCH_SIZE, stats.documentsNeedingMigration)})`);

      try {
        // Find documents that need migration
        const documentsToMigrate = await this.questionPoolModel.find({
          'optionValue.value': { $exists: true, $ne: null },
          $or: [
            { childSubject: { $exists: false } },
            { childSubject: null },
            { childSubject: '' }
          ]
        })
        .limit(this.BATCH_SIZE)
        .skip(skip)
        .exec();

        if (documentsToMigrate.length === 0) {
          this.logger.log('No more documents to migrate');
          break;
        }

        // Process each document in the batch
        for (const doc of documentsToMigrate) {
          try {
            const optionValueValue = doc.optionValue?.value;
            
            if (optionValueValue) {
              // Update the document with childSubject
              await this.questionPoolModel.updateOne(
                { _id: doc._id },
                { $set: { childSubject: optionValueValue } }
              );
              
              stats.documentsUpdated++;
              this.logger.debug(`Updated document ${doc._id}: set childSubject to "${optionValueValue}"`);
            } else {
              stats.documentsSkipped++;
              this.logger.warn(`Skipped document ${doc._id}: optionValue.value is empty`);
            }
          } catch (docError) {
            stats.errors++;
            this.logger.error(`Error updating document ${doc._id}: ${docError.message}`);
          }
        }

        stats.batchesProcessed++;
        const batchTime = Date.now() - batchStartTime;
        this.logger.log(`Batch ${batchNumber} completed in ${batchTime}ms. Updated: ${stats.documentsUpdated}, Errors: ${stats.errors}`);

        skip += this.BATCH_SIZE;
        batchNumber++;

        // Add a small delay between batches to avoid overwhelming the database
        await new Promise(resolve => setTimeout(resolve, 100));

      } catch (batchError) {
        this.logger.error(`Error processing batch ${batchNumber}: ${batchError.message}`);
        stats.errors++;
        skip += this.BATCH_SIZE; // Skip this batch and continue
      }
    }
  }

  /**
   * Verify the migration results
   */
  private async verifyMigration(stats: MigrationStats): Promise<void> {
    this.logger.log('Verifying migration results...');

    // Re-count documents after migration
    const documentsStillNeedingMigration = await this.questionPoolModel.countDocuments({
      'optionValue.value': { $exists: true, $ne: null },
      $or: [
        { childSubject: { $exists: false } },
        { childSubject: null },
        { childSubject: '' }
      ]
    });

    const documentsWithBothFields = await this.questionPoolModel.countDocuments({
      'optionValue.value': { $exists: true, $ne: null },
      childSubject: { $exists: true, $nin: [null, ''] }
    });

    this.logger.log(`Documents still needing migration: ${documentsStillNeedingMigration}`);
    this.logger.log(`Documents with both fields: ${documentsWithBothFields}`);

    if (documentsStillNeedingMigration === 0) {
      this.logger.log('✅ Migration completed successfully! All documents now have consistent fields.');
    } else {
      this.logger.warn(`⚠️ Migration incomplete. ${documentsStillNeedingMigration} documents still need migration.`);
    }
  }

  /**
   * Log final migration statistics
   */
  private logFinalStats(stats: MigrationStats): void {
    this.logger.log('='.repeat(60));
    this.logger.log('MIGRATION COMPLETED');
    this.logger.log('='.repeat(60));
    this.logger.log(`Total documents: ${stats.totalDocuments}`);
    this.logger.log(`Documents with optionValue.value: ${stats.documentsWithOptionValue}`);
    this.logger.log(`Documents with childSubject: ${stats.documentsWithChildSubject}`);
    this.logger.log(`Documents needing migration: ${stats.documentsNeedingMigration}`);
    this.logger.log(`Documents updated: ${stats.documentsUpdated}`);
    this.logger.log(`Documents skipped: ${stats.documentsSkipped}`);
    this.logger.log(`Errors encountered: ${stats.errors}`);
    this.logger.log(`Batches processed: ${stats.batchesProcessed}`);
    this.logger.log(`Total processing time: ${stats.totalProcessingTime}ms`);
    this.logger.log(`Success rate: ${stats.documentsNeedingMigration > 0 ? ((stats.documentsUpdated / stats.documentsNeedingMigration) * 100).toFixed(2) : 100}%`);
    this.logger.log('='.repeat(60));
  }
}

async function bootstrap() {
  try {
    // Create a NestJS application context
    const app = await NestFactory.createApplicationContext(AppModule);
    
    // Get the QuestionPool model
    const questionPoolModel = app.get(`${QuestionPool.name}Model`);
    
    // Create migration service
    const migrationService = new QuestionPoolMigrationService(questionPoolModel);
    
    // Run migration
    const stats = await migrationService.migrate();
    
    // Close the application context
    await app.close();
    
    // Exit with appropriate code
    if (stats.errors === 0) {
      console.log('Migration completed successfully!');
      process.exit(0);
    } else {
      console.error(`Migration completed with ${stats.errors} errors`);
      process.exit(1);
    }
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  }
}

// Run the migration if this script is executed directly
if (require.main === module) {
  bootstrap();
}

export { QuestionPoolMigrationService, MigrationStats };
