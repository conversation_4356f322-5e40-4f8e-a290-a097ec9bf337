# Question Pool Field Migration

## Overview

This migration script fixes a critical bug in the question pool system where questions cannot be retrieved due to field naming inconsistency between `optionValue.value` and `childSubject`.

## Problem Description

- **Issue**: 94.3% of questions (13,141 out of 13,939) cannot be retrieved
- **Root Cause**: Questions are stored with `optionValue.value` but queried using `childSubject` field
- **Impact**: Question pool queries return empty results, forcing the system to always generate new questions with AI

## Solution

The migration script updates existing documents to ensure both `childSubject` and `optionValue.value` fields are present and consistent.

## Migration Strategy

### Batch Processing
- Processes documents in batches of 100 to avoid overwhelming the database
- Includes delays between batches to maintain system stability
- Comprehensive error handling and logging

### Safety Features
- **Dry Run Mode**: Test the migration without making changes (`DRY_RUN=true`)
- **Rollback Strategy**: Documents can be reverted by removing the `childSubject` field if needed
- **Validation**: Pre and post-migration verification
- **Backup Recommendation**: Always backup the database before running

### Performance Considerations
- Batch size: 100 documents per batch
- Inter-batch delay: 100ms
- Estimated time: ~2-3 minutes for 13,141 documents
- Memory usage: Minimal (processes one batch at a time)

## Usage

### 1. Dry Run (Recommended First)
```bash
npm run migrate:question-pool:dry-run
```

This will:
- Analyze the current database state
- Show how many documents need migration
- Display what changes would be made
- **No actual changes to the database**

### 2. Production Migration
```bash
npm run migrate:question-pool
```

This will:
- Perform the actual migration
- Update documents in batches
- Provide real-time progress updates
- Verify results after completion

### 3. Manual Execution
```bash
# Dry run
DRY_RUN=true ts-node src/scripts/migrate-question-pool-fields.ts

# Production run
ts-node src/scripts/migrate-question-pool-fields.ts
```

## Expected Output

### Analysis Phase
```
Analyzing current database state...
Total documents in question pool: 13939
Documents with optionValue.value: 13141
Documents with childSubject: 798
Documents needing migration: 13141
```

### Migration Phase
```
Starting migration of 13141 documents...
Processing batch 1 (documents 1 to 100)
Batch 1 completed in 250ms. Updated: 100, Errors: 0
Processing batch 2 (documents 101 to 200)
...
```

### Completion
```
✅ Migration completed successfully! All documents now have consistent fields.
=============================================================
MIGRATION COMPLETED
=============================================================
Total documents: 13939
Documents with optionValue.value: 13141
Documents with childSubject: 13939
Documents needing migration: 0
Documents updated: 13141
Documents skipped: 0
Errors encountered: 0
Batches processed: 132
Total processing time: 45230ms
Success rate: 100.00%
=============================================================
```

## Verification

After migration, you can verify the fix by:

1. **Database Query**: Check that documents now have both fields
```javascript
// Before migration
db.questionpools.countDocuments({ 'optionValue.value': { $exists: true }, childSubject: { $exists: false } })
// Should return 13141

// After migration
db.questionpools.countDocuments({ 'optionValue.value': { $exists: true }, childSubject: { $exists: false } })
// Should return 0
```

2. **Application Testing**: Test question pool queries in the application
3. **Monitoring**: Check question pool metrics for improved retrieval rates

## Rollback Plan

If issues arise, you can rollback by removing the `childSubject` field from migrated documents:

```javascript
// Remove childSubject from documents that were migrated
db.questionpools.updateMany(
  { 
    'optionValue.value': { $exists: true },
    childSubject: { $exists: true }
  },
  { $unset: { childSubject: "" } }
)
```

## Monitoring

The migration includes comprehensive logging:
- Progress updates every batch
- Error tracking and reporting
- Performance metrics
- Success/failure rates

## Prerequisites

- Node.js and npm/yarn installed
- Database connection configured
- Sufficient database permissions for updates
- **Recommended**: Database backup before migration

## Troubleshooting

### Common Issues

1. **Connection Timeout**
   - Increase MongoDB connection timeout
   - Check network connectivity

2. **Memory Issues**
   - Reduce BATCH_SIZE in the script
   - Ensure sufficient system memory

3. **Permission Errors**
   - Verify database user has update permissions
   - Check MongoDB authentication

### Error Recovery

The script is designed to continue processing even if individual documents fail:
- Errors are logged but don't stop the migration
- Failed documents can be identified and processed manually
- Batch processing ensures partial completion is possible

## Post-Migration

After successful migration:
1. Monitor application logs for improved question retrieval
2. Check question pool metrics dashboard
3. Verify that AI fallback is used less frequently
4. Update documentation to reflect the fix

## Support

For issues or questions about this migration:
1. Check the application logs for detailed error messages
2. Review the migration output for specific failure details
3. Consult the development team for assistance
