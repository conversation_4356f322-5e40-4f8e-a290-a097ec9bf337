import { NestFactory } from '@nestjs/core';
import { AppModule } from '../app.module';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { QuestionPool } from '../modules/mongodb/schemas/question-pool.schema';
import { Logger } from '@nestjs/common';

/**
 * Migration script to fix missing grade information in question pool
 * Specifically targets Speed, Percentage, and Decimals questions that are missing grade field
 */

interface MigrationStats {
  totalProcessed: number;
  documentsUpdated: number;
  documentsSkipped: number;
  errors: number;
}

class GradeMigrationService {
  private readonly logger = new Logger(GradeMigrationService.name);

  constructor(
    @InjectModel(QuestionPool.name)
    private readonly questionPoolModel: Model<QuestionPool>,
  ) {}

  /**
   * Main migration method to add missing grade information
   */
  async migrateGrades(): Promise<MigrationStats> {
    const stats: MigrationStats = {
      totalProcessed: 0,
      documentsUpdated: 0,
      documentsSkipped: 0,
      errors: 0,
    };

    this.logger.log('Starting grade migration for Speed, Percentage, and Decimals questions...');

    try {
      // Find questions that are missing grade field for specific topics
      const query = {
        subject: 'Mathematics',
        childSubject: { $in: ['Speed', 'Percentage', 'Decimals'] },
        grade: { $exists: false }
      };

      const questionsToUpdate = await this.questionPoolModel.find(query).exec();
      
      this.logger.log(`Found ${questionsToUpdate.length} questions missing grade information`);

      // Process in batches to avoid memory issues
      const batchSize = 100;
      for (let i = 0; i < questionsToUpdate.length; i += batchSize) {
        const batch = questionsToUpdate.slice(i, i + batchSize);
        
        for (const question of batch) {
          try {
            // Determine appropriate grade based on content complexity
            const grade = this.determineGradeFromContent(question);
            
            await this.questionPoolModel.updateOne(
              { _id: question._id },
              { $set: { grade: grade } }
            );
            
            stats.documentsUpdated++;
            this.logger.debug(`Updated question ${question._id}: set grade to "${grade}"`);
            
          } catch (error) {
            stats.errors++;
            this.logger.error(`Error updating question ${question._id}: ${error.message}`);
          }
          
          stats.totalProcessed++;
        }
        
        // Log progress
        this.logger.log(`Processed ${Math.min(i + batchSize, questionsToUpdate.length)}/${questionsToUpdate.length} questions`);
      }

    } catch (error) {
      this.logger.error(`Migration failed: ${error.message}`, error.stack);
      throw error;
    }

    this.logger.log('Grade migration completed', stats);
    return stats;
  }

  /**
   * Determine appropriate grade based on question content and complexity
   */
  private determineGradeFromContent(question: QuestionPool): string {
    const content = question.content?.toLowerCase() || '';
    const childSubject = question.childSubject;

    // For Speed questions - typically P5-P6 level
    if (childSubject === 'Speed') {
      if (content.includes('convert') || content.includes('units') || content.includes('m/s')) {
        return 'P6'; // More advanced unit conversions
      }
      return 'P5'; // Basic speed calculations
    }

    // For Percentage questions - typically P5-P6 level
    if (childSubject === 'Percentage') {
      if (content.includes('increase') || content.includes('decrease') || content.includes('discount')) {
        return 'P6'; // More complex percentage problems
      }
      return 'P5'; // Basic percentage calculations
    }

    // For Decimals questions - typically P4-P6 level
    if (childSubject === 'Decimals') {
      if (content.includes('multiply') || content.includes('divide') || content.includes('×') || content.includes('÷')) {
        return 'P6'; // Multiplication/division with decimals
      }
      if (content.includes('add') || content.includes('subtract') || content.includes('+') || content.includes('-')) {
        return 'P5'; // Addition/subtraction with decimals
      }
      return 'P4'; // Basic decimal concepts
    }

    // Default fallback
    return 'P5';
  }

  /**
   * Verify the migration results
   */
  async verifyMigration(): Promise<void> {
    this.logger.log('Verifying migration results...');

    const topics = ['Speed', 'Percentage', 'Decimals'];
    
    for (const topic of topics) {
      const totalCount = await this.questionPoolModel.countDocuments({
        subject: 'Mathematics',
        childSubject: topic
      });

      const withGradeCount = await this.questionPoolModel.countDocuments({
        subject: 'Mathematics',
        childSubject: topic,
        grade: { $exists: true, $ne: null }
      });

      const withoutGradeCount = totalCount - withGradeCount;

      this.logger.log(`${topic}: ${withGradeCount}/${totalCount} questions have grade information (${withoutGradeCount} missing)`);
    }
  }
}

async function runMigration() {
  const app = await NestFactory.createApplicationContext(AppModule);
  const migrationService = app.get(GradeMigrationService);

  try {
    const stats = await migrationService.migrateGrades();
    await migrationService.verifyMigration();
    
    console.log('\n=== Migration Summary ===');
    console.log(`Total Processed: ${stats.totalProcessed}`);
    console.log(`Documents Updated: ${stats.documentsUpdated}`);
    console.log(`Documents Skipped: ${stats.documentsSkipped}`);
    console.log(`Errors: ${stats.errors}`);
    console.log('=========================\n');
    
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  } finally {
    await app.close();
  }
}

// Run the migration if this file is executed directly
if (require.main === module) {
  runMigration();
}

export { GradeMigrationService, MigrationStats };
